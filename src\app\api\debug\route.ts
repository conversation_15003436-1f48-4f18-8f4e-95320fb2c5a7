import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    console.log('🔍 [DEBUG] Datos recibidos en endpoint de debug:', {
      body: JSON.stringify(body, null, 2),
      timestamp: new Date().toISOString()
    });
    
    return NextResponse.json({
      success: true,
      received: body,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('❌ [DEBUG] Error en endpoint de debug:', error);
    return NextResponse.json({
      error: 'Error en debug endpoint',
      details: error instanceof Error ? error.message : 'Error desconocido'
    }, { status: 500 });
  }
}
