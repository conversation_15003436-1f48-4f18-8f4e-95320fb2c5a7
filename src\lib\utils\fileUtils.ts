/**
 * Utilidades para Manejo de Archivos
 * 
 * Este módulo proporciona funciones de utilidad para el manejo seguro de archivos,
 * incluyendo sanitización de nombres para compatibilidad con sistemas de almacenamiento
 * y URLs, siguiendo los principios de modularidad y reutilización de la arquitectura.
 */

/**
 * Extrae la extensión de un archivo del nombre completo
 * @param fileName El nombre completo del archivo
 * @returns La extensión del archivo (sin el punto) o cadena vacía si no tiene extensión
 * 
 * @example
 * extractFileExtension("documento.pdf") // "pdf"
 * extractFileExtension("archivo.tar.gz") // "gz"
 * extractFileExtension("sinextension") // ""
 */
export function extractFileExtension(fileName: string): string {
  const parts = fileName.split('.');
  return parts.length > 1 ? parts.pop() || '' : '';
}

/**
 * Valida si un nombre de archivo es válido básicamente
 * @param fileName El nombre del archivo a validar
 * @returns true si el nombre es válido, false en caso contrario
 * 
 * @example
 * validateFileName("documento.pdf") // true
 * validateFileName("") // false
 * validateFileName("   ") // false
 */
export function validateFileName(fileName: string): boolean {
  if (!fileName || typeof fileName !== 'string') {
    return false;
  }
  
  const trimmed = fileName.trim();
  if (trimmed.length === 0) {
    return false;
  }
  
  // Verificar que no contenga solo puntos
  if (/^\.+$/.test(trimmed)) {
    return false;
  }
  
  return true;
}

/**
 * Sanitiza el nombre de un archivo para que sea seguro para URLs y sistemas de almacenamiento.
 * 
 * Proceso de sanitización:
 * - Convierte a minúsculas para consistencia
 * - Elimina tildes y diacríticos (normalización Unicode)
 * - Reemplaza espacios y caracteres especiales por guiones
 * - Elimina caracteres no alfanuméricos (excepto punto y guión)
 * - Elimina guiones duplicados y guiones al inicio/final
 * - Preserva la extensión del archivo
 * - Limita la longitud del nombre base si es necesario
 * 
 * @param fileName El nombre original del archivo
 * @param maxLength Longitud máxima del nombre base (sin extensión). Por defecto 100 caracteres
 * @returns El nombre del archivo sanitizado
 * 
 * @example
 * sanitizeFileName("Tema 1 - Introducción.pdf") // "tema-1-introduccion.pdf"
 * sanitizeFileName("Constitución Española.pdf") // "constitucion-espanola.pdf"
 * sanitizeFileName("Ley 39/2015 (Procedimiento).pdf") // "ley-39-2015-procedimiento.pdf"
 * sanitizeFileName("Archivo con    espacios múltiples.txt") // "archivo-con-espacios-multiples.txt"
 */
export function sanitizeFileName(fileName: string, maxLength: number = 100): string {
  // Validar entrada
  if (!validateFileName(fileName)) {
    throw new Error('Nombre de archivo inválido');
  }
  
  // Separar el nombre del archivo de su extensión
  const parts = fileName.split('.');
  const extension = parts.length > 1 ? parts.pop() : '';
  let nombreBase = parts.join('.');
  
  // 1. Convertir a minúsculas
  nombreBase = nombreBase.toLowerCase();
  
  // 2. Reemplazar caracteres especiales comunes antes de normalizar
  nombreBase = nombreBase.replace(/nº/gi, 'no'); // Reemplazar "Nº" por "no"

  // 3. Normalizar Unicode para eliminar tildes y diacríticos
  nombreBase = nombreBase.normalize("NFD").replace(/[\u0300-\u036f]/g, "");

  // 4. Reemplazar caracteres especiales y espacios por espacios temporales
  // Esto incluye: paréntesis, corchetes, barras, dos puntos, etc.
  nombreBase = nombreBase.replace(/[^a-z0-9\s-]/g, ' ');
  
  // 5. Normalizar espacios múltiples a uno solo y convertir a guiones
  nombreBase = nombreBase.replace(/\s+/g, '-');

  // 6. Eliminar guiones duplicados
  nombreBase = nombreBase.replace(/-+/g, '-');

  // 7. Eliminar guiones al principio y al final
  nombreBase = nombreBase.replace(/^-+|-+$/g, '');

  // 8. Limitar longitud del nombre base si es necesario
  if (nombreBase.length > maxLength) {
    nombreBase = nombreBase.substring(0, maxLength);
    // Asegurar que no termine en guión después del truncado
    nombreBase = nombreBase.replace(/-+$/, '');
  }
  
  // 9. Verificar que el nombre base no esté vacío después de la sanitización
  if (nombreBase.length === 0) {
    nombreBase = 'archivo-sin-nombre';
  }

  // 10. Unir el nombre base sanitizado con la extensión
  if (extension) {
    return `${nombreBase}.${extension.toLowerCase()}`;
  }
  
  return nombreBase;
}

/**
 * Genera un nombre de archivo único añadiendo un sufijo numérico si es necesario
 * @param baseName El nombre base del archivo (ya sanitizado)
 * @param existingNames Array de nombres existentes para verificar duplicados
 * @returns Un nombre único que no existe en la lista
 * 
 * @example
 * generateUniqueFileName("documento.pdf", ["documento.pdf"]) // "documento-1.pdf"
 * generateUniqueFileName("archivo.txt", ["archivo.txt", "archivo-1.txt"]) // "archivo-2.txt"
 */
export function generateUniqueFileName(baseName: string, existingNames: string[]): string {
  if (!existingNames.includes(baseName)) {
    return baseName;
  }
  
  const parts = baseName.split('.');
  const extension = parts.length > 1 ? parts.pop() : '';
  const nameWithoutExt = parts.join('.');
  
  let counter = 1;
  let uniqueName: string;
  
  do {
    const suffix = `-${counter}`;
    uniqueName = extension 
      ? `${nameWithoutExt}${suffix}.${extension}`
      : `${nameWithoutExt}${suffix}`;
    counter++;
  } while (existingNames.includes(uniqueName));
  
  return uniqueName;
}

/**
 * Obtiene información detallada de un archivo
 * @param file El objeto File
 * @returns Objeto con información del archivo incluyendo nombre sanitizado
 */
export function getFileInfo(file: File) {
  const originalName = file.name;
  const extension = extractFileExtension(originalName);
  const isValid = validateFileName(originalName);

  // Solo sanitizar si el nombre es válido, de lo contrario usar un nombre por defecto
  let sanitizedName: string;
  try {
    sanitizedName = isValid ? sanitizeFileName(originalName) : 'archivo-sin-nombre';
  } catch {
    sanitizedName = 'archivo-sin-nombre';
  }

  return {
    originalName,
    sanitizedName,
    extension,
    size: file.size,
    type: file.type,
    isValid,
    lastModified: file.lastModified
  };
}
