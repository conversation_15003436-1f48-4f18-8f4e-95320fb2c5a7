// src/config/vertexai.ts
// Configuración centralizada para Vertex AI y los modelos Gemini 2.5

export const VERTEX_AI_CONFIG = {
  // **IMPORTANTE**: Asegúrate de que esta región sea europea para el RGPD
  location: process.env.GCP_LOCATION || 'europe-west1', 
  projectId: process.env.GCP_PROJECT_ID,
};

// Modelos de la familia Gemini 2.5 disponibles
export const GEMINI_MODELS = {
  // Modelo más potente y versátil
  PRO: 'gemini-2.5-pro',
  // Modelo más rápido y económico
  FLASH: 'gemini-2.5-flash', 
};

// Asignación de modelos por funcionalidad para optimizar coste y rendimiento
export const MODELS_BY_TASK = {
  PLAN_ESTUDIOS: GEMINI_MODELS.FLASH,       // Requiere alta precisión
  CONVERSACIONES: GEMINI_MODELS.FLASH,   // Requiere rapidez
  FLASHCARDS: GEMINI_MODELS.FLASH,       // Tarea simple, rapidez
  TESTS: GEMINI_MODELS.PRO,              // Requiere alta precisión
  MAPAS_MENTALES: GEMINI_MODELS.FLASH,     // Requiere creatividad y estructura
  RESUMENES: GEMINI_MODELS.PRO,          // Requiere alta capacidad de síntesis
};

// Configuraciones de generación por defecto y por tarea
export const GENERATION_CONFIGS = {
  DEFAULTS: {
    maxOutputTokens: 8192,
    temperature: 0.7,
    topP: 0.95,
  },
  PLAN_ESTUDIOS: { temperature: 0.3, maxOutputTokens: 65536 },
  CONVERSACIONES: { temperature: 0.8, maxOutputTokens: 65536 },
  FLASHCARDS: { temperature: 0.6, maxOutputTokens: 65536 },
  TESTS: { temperature: 0.4, maxOutputTokens: 65536 },
  MAPAS_MENTALES: { temperature: 0.8, maxOutputTokens: 65536 },
  RESUMENES: { temperature: 0.5, maxOutputTokens: 65536 },
};

// Función helper para obtener la configuración completa para una tarea
export function getVertexAIConfig(taskType: keyof typeof MODELS_BY_TASK) {
  return {
    model: MODELS_BY_TASK[taskType],
    ...GENERATION_CONFIGS.DEFAULTS,
    ...GENERATION_CONFIGS[taskType],
  };
}
