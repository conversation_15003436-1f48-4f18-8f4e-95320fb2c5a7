import { createContext, useContext, useState, useEffect, ReactNode, useCallback, useRef } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { supabase } from '@/lib/supabase/supabaseClient';
import {
  iniciarSesion as iniciarSesionService,
  cerrarSesion as cerrarSesionService,
} from '@/lib/supabase/authService';
import { User, Session } from '@supabase/supabase-js';
import { useAutoLogout } from '@/hooks/useInactivityTimer';

interface AuthContextType {
  user: User | null;
  session: Session | null;
  isLoading: boolean;
  error: string | null;
  iniciarSesion: (email: string, password_provided: string) => Promise<{ user: User | null; session: Session | null; error: string | null }>;
  cerrarSesion: () => Promise<void>;
  estaAutenticado: () => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

// Función para comparación profunda de usuarios
const areUsersEqual = (user1: User | null, user2: User | null): boolean => {
  if (user1 === user2) return true;
  if (!user1 || !user2) return false;

  return (
    user1.id === user2.id &&
    user1.email === user2.email &&
    user1.email_confirmed_at === user2.email_confirmed_at &&
    user1.last_sign_in_at === user2.last_sign_in_at &&
    JSON.stringify(user1.user_metadata) === JSON.stringify(user2.user_metadata)
  );
};

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(true); // Start true: loading initial auth state
  const [error, setError] = useState<string | null>(null);

  const router = useRouter();
  const pathname = usePathname();

  // Ref para mantener referencia del usuario anterior para comparación
  const previousUserRef = useRef<User | null>(null);

  // Effect for auth state listener and initial session check
  useEffect(() => {
    setIsLoading(true); // Explicitly set loading true at the start of auth setup

    const { data: authListener } = supabase.auth.onAuthStateChange(
      (event, currentSession) => {
        setSession(currentSession);

        // Implementar comparación profunda para evitar re-renders innecesarios
        const newUser = currentSession?.user ?? null;
        if (!areUsersEqual(previousUserRef.current, newUser)) {
          setUser(newUser);
          previousUserRef.current = newUser;
        }

        setError(null); // Clear previous errors on any auth state change

        // Centralize setIsLoading(false) after processing the event.
        if (event === 'INITIAL_SESSION' || event === 'SIGNED_IN' || event === 'SIGNED_OUT' || event === 'TOKEN_REFRESHED' || event === 'USER_UPDATED' || event === 'PASSWORD_RECOVERY') {
            setIsLoading(false);
        }
      }
    );

    // Initial session fetch. onAuthStateChange with INITIAL_SESSION will also fire.
    supabase.auth.getSession().then(({ data: { session: initialSessionCheck }, error: getSessionError }) => {
        if (getSessionError) {
            setError(getSessionError.message);
            setIsLoading(false); // Ensure loading is false if initial getSession fails
        }

        // Para dispositivos móviles, verificar también localStorage si no hay sesión
        if (!initialSessionCheck && typeof window !== 'undefined') {
          try {
            const storedToken = localStorage.getItem('supabase.auth.token');
            if (storedToken) {
              // Forzar una verificación de sesión adicional después de un breve delay
              setTimeout(async () => {
                try {
                  const { data: { session: retrySession } } = await supabase.auth.getSession();
                  if (retrySession) {
                    setSession(retrySession);
                    // Aplicar comparación profunda también aquí
                    const retryUser = retrySession.user;
                    if (!areUsersEqual(previousUserRef.current, retryUser)) {
                      setUser(retryUser);
                      previousUserRef.current = retryUser;
                    }
                    setIsLoading(false);
                  }
                } catch (retryError) {
                  // Error silencioso
                }
              }, 200);
            }
          } catch (e) {
            // Error silencioso
          }
        }

        // If INITIAL_SESSION hasn't fired and set loading to false, and this fails, we ensure it's false.
        // Note: if getSession is successful, `setIsLoading(false)` is primarily handled by INITIAL_SESSION event.
    }).catch(error => {
        setError(error.message);
        setIsLoading(false); // Ensure loading is false if initial getSession throws
    });

    return () => {
      authListener?.subscription.unsubscribe();
    };
  }, []); // Runs once on mount

  // Effect for handling redirections based on auth state and pathname
  useEffect(() => {
    // No realizar redirecciones mientras se está cargando
    if (isLoading) {
      return;
    }

    // No aplicar redirecciones a rutas de API o recursos estáticos
    if (pathname.startsWith('/api') || pathname.startsWith('/_next')) {
        return;
    }

    // Definir rutas públicas (mantener sincronizado con middleware)
    // src/contexts/AuthContext.tsx (CORREGIDO)

const publicPaths = [
  '/',
  '/login',
  '/payment',
  '/thank-you',
  '/politica-de-cookies',
  '/politica-de-privacidad',
  '/terminos-de-servicio',
  '/auth/callback',
  '/auth/confirmed',     // Ya debería estar
  '/auth/unauthorized',   // Ya debería estar
  '/auth/reset-password', // Importante para el flujo de establecimiento de contraseña
  '/auth/confirm-reset',    // Importante para el flujo de establecimiento de contraseña
  '/auth/confirm-invitation' // <-- **AÑADIR ESTA LÍNEA**
];
    // Si hay sesión y estamos en /login, redirigir a la aplicación
    // Esta es una salvaguarda del lado del cliente.
    if (session && pathname === '/login') {
      router.replace('/app'); // router.replace es mejor aquí para evitar entradas en el historial
      return; // Importante retornar para no evaluar la siguiente condición
    }

    // Si NO hay sesión y NO estamos en una ruta pública (y no es una ruta API/interna)
    // Esta lógica es para cuando el estado cambia en el cliente (ej. logout)
    if (!session && !publicPaths.includes(pathname) && !pathname.startsWith('/api') && !pathname.startsWith('/_next')) {
      router.replace('/login');
      return;
    }
  }, [session, isLoading, pathname, router]);

  const iniciarSesion = useCallback(async (email: string, password_provided: string) => {
    setIsLoading(true);
    setError(null);
    try {
      const { user: loggedInUser, session: currentAuthSession, error: loginError } = await iniciarSesionService(email, password_provided);

      if (loginError) {
        setError(loginError);
        setIsLoading(false); // Ensure loading is false on error
        return { user: null, session: null, error: loginError };
      }

      // Verificar que la sesión se haya establecido correctamente antes de redirigir
      if (currentAuthSession) {
        // Esperar un momento adicional para asegurar que las cookies se propaguen
        // antes de la redirección
        await new Promise(resolve => setTimeout(resolve, 300));

        // Redirigir a la aplicación usando replace para evitar entradas en el historial
        router.replace('/app');
      }

      // If successful, onAuthStateChange (SIGNED_IN) will set user, session, and isLoading to false.
      return { user: loggedInUser, session: currentAuthSession, error: null };

    } catch (e: any) {
      const errorMessage = (e instanceof Error && e.message) ? e.message : 'Error desconocido durante el inicio de sesión.';
      setError(errorMessage);
      setIsLoading(false); // Ensure loading is false on exception
      return { user: null, session: null, error: errorMessage };
    }
  }, [router]); // Added router dependency

  const cerrarSesion = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    const { error: logoutError } = await cerrarSesionService();
    if (logoutError) {
      setError(logoutError);
      setIsLoading(false); // Ensure loading is false on error
    }
    // If successful, onAuthStateChange (SIGNED_OUT) handles state updates and isLoading.
    // The redirection useEffect will then handle redirecting to /login.
  }, []); // Assuming cerrarSesionService is a stable import

  const estaAutenticado = useCallback(() => !!user && !!session && !isLoading, [user, session, isLoading]);

  // Función para manejar el logout por inactividad
  const handleInactivityLogout = useCallback(async () => {
    // Cerrar sesión directamente sin mostrar advertencia
    await cerrarSesion();
  }, [cerrarSesion]);

  // Hook para manejar la inactividad (solo si el usuario está autenticado)
  const { resetTimer } = useAutoLogout(
    20, // 20 minutos de inactividad antes de cerrar sesión directamente
    handleInactivityLogout,
    estaAutenticado() // Solo activo si está autenticado
  );

  const value = {
    user,
    session,
    isLoading,
    error,
    iniciarSesion,
    cerrarSesion,
    estaAutenticado,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth debe ser utilizado dentro de un AuthProvider');
  }
  return context;
};
