import { prepararDocumentos } from '../vertexai/vertexAiClient';
import { PROMPT_PREGUNTAS } from '../../config/prompts';
import { llamarGemini } from '../vertexai/vertexAiClient';
import { getVertexAIConfig } from '@/config/vertexai';
import { TokenEstimationService } from '../services/tokenEstimationService';
import { ChunkingService } from '../services/chunkingService';
import type { Chunk } from '@/types/chunking';

/**
 * Límite de tokens para chat (conservador para evitar errores de rate limit)
 */
const MAX_CHAT_TOKENS = 25000;

/**
 * Obtiene una respuesta de la IA a una pregunta sobre los documentos
 * Maneja automáticamente el chunking para evitar límites de tokens
 */
export async function obtenerRespuestaIA(
  pregunta: string,
  documentos: { titulo: string; contenido: string; categoria?: string; numero_tema?: number }[]
): Promise<string> {
  try {
    // Verificar que la pregunta sea válida
    if (!pregunta || typeof pregunta !== 'string' || pregunta.trim() === '') {
      console.warn('Se recibió una pregunta vacía o inválida');
      return "Por favor, proporciona una pregunta válida.";
    }

    // Verificar que los documentos sean válidos
    if (!documentos || !Array.isArray(documentos) || documentos.length === 0) {
      console.warn('No se proporcionaron documentos válidos para obtenerRespuestaIA');
      return "No se han proporcionado documentos para responder a esta pregunta.";
    }

    // Preparar el contenido de los documentos con chunking inteligente
    const resultadoDocumentos = prepararDocumentos(documentos, true, 'temario'); // Forzar chunking para documentos grandes

    // Obtener configuración específica para conversaciones
    const config = getVertexAIConfig('CONVERSACIONES');
    console.log(`💬 Generando respuesta con modelo: ${config.model} (maxOutputTokens: ${config.maxOutputTokens})`);

    // Si el documento fue chunkeado, procesar de manera inteligente
    if (resultadoDocumentos.wasChunked && Array.isArray(resultadoDocumentos.content)) {
      console.log(`🧩 Documento chunkeado en ${resultadoDocumentos.content.length} partes. Procesando de manera inteligente...`);

      return await procesarPreguntaConChunks(pregunta, resultadoDocumentos.content, config);
    } else {
      // Procesamiento tradicional para documentos pequeños
      const contenidoDocumentos = Array.isArray(resultadoDocumentos.content)
        ? resultadoDocumentos.content.join('\n\n')
        : resultadoDocumentos.content;

      if (!contenidoDocumentos) {
        console.warn('No se pudo preparar el contenido de los documentos');
        return "No se han podido procesar los documentos proporcionados. Por favor, verifica que los documentos contengan información válida.";
      }

      // Verificar que no exceda límites de tokens
      const estimacion = TokenEstimationService.estimateForChat([contenidoDocumentos]);
      if (estimacion.totalEstimated > MAX_CHAT_TOKENS) {
        console.warn(`⚠️ Contenido demasiado grande (${estimacion.totalEstimated} tokens), forzando chunking`);
        // Forzar chunking si el contenido es demasiado grande
        const resultadoForzado = prepararDocumentos(documentos, true, 'temario');
        if (Array.isArray(resultadoForzado.content)) {
          return await procesarPreguntaConChunks(pregunta, resultadoForzado.content, config);
        }
      }

      // Construir el prompt para la IA
      const prompt = PROMPT_PREGUNTAS
        .replace('{documentos}', contenidoDocumentos)
        .replace('{pregunta}', pregunta);

      // Generar la respuesta usando Vertex AI
      return await llamarGemini(prompt, {
        ...config,
        activityName: 'Conversación/Q&A'
      });
    }
  } catch (error) {
    console.error('Error al obtener respuesta de la IA:', error);

    // Proporcionar un mensaje de error más descriptivo si es posible
    if (error instanceof Error) {
      return `Lo siento, ha ocurrido un error al procesar tu pregunta: ${error.message}. Por favor, inténtalo de nuevo más tarde.`;
    }

    return "Lo siento, ha ocurrido un error al procesar tu pregunta. Por favor, inténtalo de nuevo más tarde.";
  }
}

/**
 * Procesa una pregunta usando chunks de manera inteligente
 * Busca en chunks relevantes y combina las respuestas
 */
async function procesarPreguntaConChunks(
  pregunta: string,
  chunks: Chunk[],
  config: any
): Promise<string> {
  try {
    // Usar el nuevo ChunkingService para procesar chunks
    const resultado = await ChunkingService.procesarConChunks(chunks, pregunta, 'CONVERSACIONES');

    console.log(`💬 Chunks procesados: ${resultado.chunksUsados} de ${resultado.totalChunks} (estrategia: ${resultado.estrategiaUsada})`);

    // Construir el prompt para la IA
    const prompt = PROMPT_PREGUNTAS
      .replace('{documentos}', resultado.contenido)
      .replace('{pregunta}', pregunta);

    const respuesta = await llamarGemini(prompt, {
      ...config,
      activityName: `Conversación/Q&A - ${resultado.chunksUsados}/${resultado.totalChunks} chunks (${resultado.estrategiaUsada})`
    });

    // Si hay una nota del ChunkingService, agregarla a la respuesta
    if (resultado.nota) {
      return respuesta + `\n\n*Nota: ${resultado.nota}*`;
    }

    return respuesta;

  } catch (error) {
    console.error('Error procesando pregunta con chunks:', error);
    throw error;
  }
}


