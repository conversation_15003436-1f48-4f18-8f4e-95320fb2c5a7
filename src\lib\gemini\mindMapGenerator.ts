import { prepararDocumentos } from '../vertexai/vertexAiClient';
import { PROMPT_MAPAS_MENTALES } from '@/config/prompts';
import { llamarGemini } from '../vertexai/vertexAiClient';
import { getVertexAIConfig } from '@/config/vertexai';
import { type Chunk } from '@/lib/utils/textProcessing';
import { ChunkingService } from '../services/chunkingService';
import * as Sentry from "@sentry/nextjs";

/**
 * Genera un mapa mental a partir de los documentos
 */
export async function generarMapaMental(
  documentos: { titulo: string; contenido: string; categoria?: string; numero_tema?: number }[],
  instrucciones?: string
): Promise<any> {
  try {
    // Validar entrada
    if (!documentos || documentos.length === 0) {
      throw new Error("No se han proporcionado documentos para generar el mapa mental.");
    }

    // Preparar el contenido de los documentos
    const resultadoDocumentos = prepararDocumentos(documentos);

    // Verificar si se usó chunking
    if (resultadoDocumentos.wasChunked && Array.isArray(resultadoDocumentos.content)) {
      console.log(`🗺️ Procesando mapa mental con chunking: ${resultadoDocumentos.content.length} chunks`);
      return await procesarMapaMentalConChunks(resultadoDocumentos.content, instrucciones, documentos);
    } else {
      // Procesamiento tradicional para documentos sin chunking
      const contenidoDocumentos = Array.isArray(resultadoDocumentos.content)
        ? resultadoDocumentos.content.join('\n\n')
        : resultadoDocumentos.content;

      if (!contenidoDocumentos || contenidoDocumentos.trim().length === 0) {
        throw new Error("El contenido de los documentos está vacío o no es válido.");
      }

      console.log(`🗺️ Procesando mapa mental sin chunking`);
      return await procesarMapaMentalSinChunks(contenidoDocumentos, instrucciones);
    }

  } catch (error) {
    console.error('Error al generar mapa mental:', error);
    throw error;
  }
}

/**
 * Procesa mapa mental con chunking - usa el sistema simple y robusto de ChunkingService
 */
async function procesarMapaMentalConChunks(
  chunks: Chunk[],
  instrucciones?: string,
  documentos?: { titulo: string; contenido: string; categoria?: string; numero_tema?: number }[]
): Promise<any> {
  // Preparar instrucciones limpias
  const instruccionesLimpias = instrucciones?.trim() || 'Crea un mapa mental que organice los conceptos principales del contenido.';

  // Usar el nuevo ChunkingService para procesar chunks
  const resultado = await ChunkingService.procesarConChunks(chunks, instruccionesLimpias, 'MAPAS_MENTALES');

  console.log(`🗺️ Chunks procesados: ${resultado.chunksUsados} de ${resultado.totalChunks} (estrategia: ${resultado.estrategiaUsada})`);

  // Usar el contenido procesado por ChunkingService
  const combinedContent = resultado.contenido;

  // Preparar el prompt para la generación
  let finalPrompt = PROMPT_MAPAS_MENTALES.replace('{documentos}', combinedContent);
  finalPrompt = finalPrompt.replace('{instrucciones}', instruccionesLimpias);

  // Generar el mapa mental usando el contenido procesado
  const config = getVertexAIConfig('MAPAS_MENTALES');
  const responseText = await llamarGemini(finalPrompt, {
    ...config,
    activityName: `Generación de Mapa Mental - ${resultado.chunksUsados}/${resultado.totalChunks} chunks (${resultado.estrategiaUsada})`
  });

  // PASO 5: Parsear la respuesta directamente (ya no se necesita combinar)
  // Validar que la respuesta no esté vacía
  if (!responseText || responseText.trim().length === 0) {
    throw new Error("La IA no generó ningún contenido para el mapa mental.");
  }

  // Extraer y limpiar la respuesta
  let htmlContent = responseText.trim();

  // Buscar el HTML en la respuesta (puede estar envuelto en markdown)
  const htmlMatch = htmlContent.match(/<!DOCTYPE html>[\s\S]*<\/html>/i);
  if (htmlMatch) {
    htmlContent = htmlMatch[0];
  }

  // Limpiar marcadores de código markdown si existen
  htmlContent = htmlContent
    .replace(/```html/gi, '')
    .replace(/```/g, '')
    .trim();

  if (!htmlContent || htmlContent.length === 0) {
    throw new Error("No se pudo extraer contenido HTML válido del mapa mental generado.");
  }

  console.log(`🗺️ Mapa mental generado con chunking combinado: ${htmlContent.length} caracteres`);
  return htmlContent;
}

/**
 * Procesa mapa mental sin chunking - método tradicional
 */
async function procesarMapaMentalSinChunks(
  contenidoDocumentos: string,
  instrucciones?: string
): Promise<any> {
  // Validar y limpiar instrucciones
  const instruccionesLimpias = instrucciones?.trim() || 'Crea un mapa mental que organice los conceptos principales del contenido.';

  // Construir el prompt final con validación
  let finalPrompt = PROMPT_MAPAS_MENTALES.replace('{documentos}', contenidoDocumentos);
  finalPrompt = finalPrompt.replace('{instrucciones}', instruccionesLimpias);

  // Obtener configuración específica para mapas mentales
  const config = getVertexAIConfig('MAPAS_MENTALES');

  console.log(`🗺️ Generando mapa mental con modelo: ${config.model} (maxOutputTokens: ${config.maxOutputTokens})`);

  // Generar el mapa mental usando Vertex AI con la configuración correcta
  const responseText = await llamarGemini(finalPrompt, {
    ...config,
    activityName: 'Generación de Mapa Mental'
  });

  // Validar que la respuesta no esté vacía
  if (!responseText || responseText.trim().length === 0) {
    throw new Error("La IA no generó ningún contenido para el mapa mental.");
  }

  // Extraer y limpiar la respuesta (sin validaciones restrictivas)
  let htmlContent = responseText.trim();

  // Buscar el HTML en la respuesta (puede estar envuelto en markdown)
  const htmlMatch = htmlContent.match(/<!DOCTYPE html>[\s\S]*<\/html>/i);
  if (htmlMatch) {
    htmlContent = htmlMatch[0];
  }

  // Limpiar marcadores de código markdown si existen
  htmlContent = htmlContent
    .replace(/```html/gi, '')
    .replace(/```/g, '')
    .trim();

  // Log para debugging - mostrar lo que generó la IA
  console.log('Contenido generado por la IA (primeros 500 caracteres):', htmlContent.substring(0, 500));
  console.log('Longitud total del contenido:', htmlContent.length);

  // Validar que el HTML sea válido
  if (!htmlContent.includes('<!DOCTYPE html>') || !htmlContent.includes('</html>')) {
    console.warn('⚠️ El contenido generado no parece ser HTML válido');
    console.log('Contenido completo:', htmlContent);
  } else {
    console.log('✅ HTML válido detectado');
  }

  // Retornar el contenido tal como lo generó la IA (sin validaciones)
  return htmlContent;
}
