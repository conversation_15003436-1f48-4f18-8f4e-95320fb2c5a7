'use client';

import { useState, useEffect, useCallback } from 'react';

/**
 * Hook para rastrear si un componente o modal ya ha sido visto
 * durante la sesión actual del navegador.
 * @param key - La clave única para almacenar en sessionStorage.
 * @returns Un objeto con el estado `shouldShow` y la función `markAsViewed`.
 */
export function useSessionViewTracker(key: string) {
  const [shouldShow, setShouldShow] = useState(false);

  useEffect(() => {
    // Esta comprobación se ejecuta solo una vez, cuando el componente se monta en el cliente.
    try {
      const hasBeenViewed = sessionStorage.getItem(key);
      if (!hasBeenViewed) {
        setShouldShow(true);
      }
    } catch (error) {
      console.error('Error al acceder a sessionStorage:', error);
    }
  }, [key]);

  const markAsViewed = useCallback(() => {
    try {
      sessionStorage.setItem(key, 'true');
      setShouldShow(false);
    } catch (error) {
      console.error('Error al escribir en sessionStorage:', error);
    }
  }, [key]);

  return { shouldShow, markAsViewed };
}
