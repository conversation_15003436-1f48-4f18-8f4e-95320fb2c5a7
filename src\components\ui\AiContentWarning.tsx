'use client';

import React from 'react';
import { FiAlertTriangle } from 'react-icons/fi';

interface AiContentWarningProps {
  className?: string;
}

const AiContentWarning: React.FC<AiContentWarningProps> = ({ className = '' }) => {
  return (
    <div className={`bg-yellow-50 border-l-4 border-yellow-400 p-4 my-4 ${className}`}>
      <div className="flex">
        <div className="flex-shrink-0">
          <FiAlertTriangle className="h-5 w-5 text-yellow-500" aria-hidden="true" />
        </div>
        <div className="ml-3">
          <p className="text-sm font-bold text-yellow-800">
            Atención: Contenido Generado por IA
          </p>
          <p className="mt-1 text-sm text-yellow-700">
            Este material ha sido creado por Inteligencia Artificial para facilitar tu estudio.
            Aunque es una herramienta poderosa, puede cometer errores o generar imprecisiones. 
            Te recomendamos encarecidamente verificar siempre la información con tus fuentes de estudio oficiales.
          </p>
        </div>
      </div>
    </div>
  );
};

export default AiContentWarning;
