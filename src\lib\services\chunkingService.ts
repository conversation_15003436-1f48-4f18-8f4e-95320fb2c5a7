/**
 * Servicio Unificado de Chunking
 * 
 * Centraliza la lógica de procesamiento de chunks para todas las funciones de generación,
 * implementando el sistema simple y robusto usado originalmente en questionService.ts.
 * 
 * Sigue los patrones de ARCHITECTURE.md - Service Layer Pattern
 */

import { type Chunk } from '@/types/chunking';
import { TokenEstimationService } from './tokenEstimationService';

/**
 * Configuración de límites de tokens por tipo de generación
 */
export interface ChunkProcessingConfig {
  maxTokens: number;
  maxChunks?: number;
  truncateSize?: number;
}

/**
 * Resultado del procesamiento de chunks
 */
export interface ChunkProcessingResult {
  contenido: string;
  chunksUsados: number;
  totalChunks: number;
  estrategiaUsada: 'relevantes' | 'fallback' | 'truncado';
  tokensEstimados: number;
  nota?: string;
}

/**
 * Servicio de Chunking - Implementa Service Layer Pattern
 */
export class ChunkingService {
  /**
   * Configuración por tipo de generación
   */
  private static readonly CONFIG: Record<string, ChunkProcessingConfig> = {
    CONVERSACIONES: { 
      maxTokens: 25000,
      maxChunks: 10,
      truncateSize: 15000
    },
    TESTS: { 
      maxTokens: 30000,
      maxChunks: 10,
      truncateSize: 18000
    },
    FLASHCARDS: { 
      maxTokens: 25000,
      maxChunks: 10,
      truncateSize: 15000
    },
    MAPAS_MENTALES: { 
      maxTokens: 35000,
      maxChunks: 12,
      truncateSize: 20000
    }
  };

  /**
   * Extrae palabras clave relevantes de una consulta/pregunta
   * Migrado desde questionService.ts
   */
  static extraerPalabrasClave(consulta: string): string[] {
    // Convertir a minúsculas y eliminar caracteres especiales
    const consultaLimpia = consulta.toLowerCase()
      .replace(/[^\w\sáéíóúñü]/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();

    // Palabras comunes a filtrar
    const palabrasComunes = new Set([
      'el', 'la', 'los', 'las', 'un', 'una', 'unos', 'unas',
      'de', 'del', 'en', 'con', 'por', 'para', 'que', 'se',
      'es', 'son', 'está', 'están', 'como', 'qué', 'cuál',
      'explícame', 'explica', 'dime', 'cuéntame', 'háblame',
      'sobre', 'acerca', 'respecto', 'tema', 'apartado', 'sección'
    ]);

    // Extraer palabras de 3+ caracteres que no sean comunes
    const palabras = consultaLimpia.split(' ')
      .filter(palabra => palabra.length >= 3 && !palabrasComunes.has(palabra));

    // Buscar también números de apartados/secciones
    const numerosApartados = consulta.match(/\d+\.?\d*/g) || [];

    return [...palabras, ...numerosApartados];
  }

  /**
   * Encuentra chunks relevantes basándose en palabras clave
   * Migrado desde questionService.ts
   */
  static encontrarChunksRelevantes(chunks: Chunk[], palabrasClave: string[]): string[] {
    if (palabrasClave.length === 0) {
      return [];
    }

    const chunksConPuntuacion = chunks.map((chunk, index) => {
      let puntuacion = 0;
      const chunkLowerCase = chunk.content.toLowerCase();

      // Contar coincidencias de palabras clave
      for (const palabra of palabrasClave) {
        const regex = new RegExp(`\\b${palabra.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}`, 'gi');
        const coincidencias = (chunkLowerCase.match(regex) || []).length;
        puntuacion += coincidencias;
      }

      return { chunk: chunk.content, index, puntuacion };
    });

    // Filtrar chunks con al menos una coincidencia y ordenar por puntuación
    const chunksRelevantes = chunksConPuntuacion
      .filter(item => item.puntuacion > 0)
      .sort((a, b) => b.puntuacion - a.puntuacion)
      .slice(0, 10) // Máximo 10 chunks más relevantes
      .sort((a, b) => a.index - b.index) // Mantener orden original
      .map(item => item.chunk);

    return chunksRelevantes;
  }

  /**
   * Procesa chunks usando el sistema simple y robusto de questionService.ts
   * Implementa las dos estrategias: chunks relevantes + fallback
   */
  static async procesarConChunks(
    chunks: Chunk[],
    consulta: string,
    tipo: string
  ): Promise<ChunkProcessingResult> {
    try {
      const config = this.CONFIG[tipo] || this.CONFIG.CONVERSACIONES;
      
      console.log(`🔍 [${tipo}] Buscando información relevante en ${chunks.length} chunks...`);

      // Estrategia 1: Buscar chunks que contengan palabras clave de la consulta
      const palabrasClave = this.extraerPalabrasClave(consulta);
      const chunksRelevantes = this.encontrarChunksRelevantes(chunks, palabrasClave);

      console.log(`📋 [${tipo}] Encontrados ${chunksRelevantes.length} chunks relevantes de ${chunks.length} totales`);

      // Si encontramos chunks relevantes, usar solo esos
      if (chunksRelevantes.length > 0) {
        const contenidoRelevante = chunksRelevantes.join('\n\n');

        // Verificar que no exceda límites
        const estimacion = TokenEstimationService.estimateForChat([contenidoRelevante]);
        if (estimacion.totalEstimated <= config.maxTokens) {
          console.log(`✅ [${tipo}] Usando ${chunksRelevantes.length} chunks relevantes (${estimacion.totalEstimated} tokens estimados)`);

          return {
            contenido: contenidoRelevante,
            chunksUsados: chunksRelevantes.length,
            totalChunks: chunks.length,
            estrategiaUsada: 'relevantes',
            tokensEstimados: estimacion.totalEstimated
          };
        }
      }

      // Estrategia 2: Si hay demasiados chunks relevantes o no encontramos ninguno,
      // usar los primeros chunks hasta el límite de tokens
      console.log(`⚠️ [${tipo}] Usando estrategia de fallback: primeros chunks hasta límite de tokens`);

      let contenidoAcumulado = '';
      let chunksUsados = 0;

      for (const chunk of chunks) {
        const contenidoTemporal = contenidoAcumulado + (contenidoAcumulado ? '\n\n' : '') + chunk.content;
        const estimacion = TokenEstimationService.estimateForChat([contenidoTemporal]);

        if (estimacion.totalEstimated > config.maxTokens) {
          break;
        }

        contenidoAcumulado = contenidoTemporal;
        chunksUsados++;
      }

      if (!contenidoAcumulado) {
        // Si ni siquiera el primer chunk cabe, usar solo una parte
        contenidoAcumulado = chunks[0].content.substring(0, config.truncateSize || 15000);
        chunksUsados = 1;
        
        console.log(`📄 [${tipo}] Chunk truncado a ${config.truncateSize} caracteres`);

        return {
          contenido: contenidoAcumulado,
          chunksUsados: 1,
          totalChunks: chunks.length,
          estrategiaUsada: 'truncado',
          tokensEstimados: TokenEstimationService.estimateForChat([contenidoAcumulado]).totalEstimated
        };
      }

      console.log(`📄 [${tipo}] Usando ${chunksUsados} chunks (de ${chunks.length}) para procesar`);

      const estimacionFinal = TokenEstimationService.estimateForChat([contenidoAcumulado]);
      
      // Generar nota si no usamos todos los chunks
      let nota: string | undefined;
      if (chunksUsados < chunks.length) {
        nota = `Esta respuesta se basa en ${chunksUsados} de ${chunks.length} secciones del documento. Si necesitas información de otras secciones específicas, por favor hazme una pregunta más específica.`;
      }

      return {
        contenido: contenidoAcumulado,
        chunksUsados,
        totalChunks: chunks.length,
        estrategiaUsada: 'fallback',
        tokensEstimados: estimacionFinal.totalEstimated,
        nota
      };

    } catch (error) {
      console.error(`Error procesando chunks para ${tipo}:`, error);
      throw error;
    }
  }

  /**
   * Obtiene la configuración para un tipo específico de generación
   */
  static getConfig(tipo: string): ChunkProcessingConfig {
    return this.CONFIG[tipo] || this.CONFIG.CONVERSACIONES;
  }
}
