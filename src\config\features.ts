// src/config/features.ts
// Configuración centralizada de características y funcionalidades

// ============================================================================
// CONSTANTES DE FEATURES
// ============================================================================

/**
 * Identificadores únicos de características del sistema
 */
export const FEATURE_IDS = {
  DOCUMENT_UPLOAD: 'document_upload',
  TEST_GENERATION: 'test_generation',
  FLASHCARD_GENERATION: 'flashcard_generation',
  MIND_MAP_GENERATION: 'mind_map_generation',
  AI_TUTOR_CHAT: 'ai_tutor_chat',
  STUDY_PLANNING: 'study_planning',
  SUMMARY_A1_A2: 'summary_a1_a2'
} as const;

/**
 * Tipo para los IDs de características
 */
export type FeatureId = typeof FEATURE_IDS[keyof typeof FEATURE_IDS];

/**
 * Acciones que pueden realizarse en el sistema
 */
export const ACTION_TYPES = {
  TEST_GENERATION: 'test_generation',
  FLASHCARD_GENERATION: 'flashcard_generation',
  MIND_MAP_GENERATION: 'mind_map_generation',
  AI_CHAT: 'ai_chat',
  STUDY_PLANNING: 'study_planning',
  SUMMARY_GENERATION: 'summary_generation'
} as const;

export type ActionType = typeof ACTION_TYPES[keyof typeof ACTION_TYPES];

// ============================================================================
// CONFIGURACIÓN DE FEATURES
// ============================================================================

/**
 * Configuración completa de una característica
 */
export interface FeatureConfig {
  id: FeatureId;
  name: string;
  displayName: string;
  description: string;
  category: 'core' | 'premium' | 'advanced';
  minimumPlans: string[];
  requiresPayment: boolean;
  tokensRequired: number;
  icon?: string;
  route?: string;
}

/**
 * Configuración de todas las características del sistema
 */
export const FEATURES_CONFIG: Record<FeatureId, FeatureConfig> = {
  [FEATURE_IDS.DOCUMENT_UPLOAD]: {
    id: FEATURE_IDS.DOCUMENT_UPLOAD,
    name: 'document_upload',
    displayName: 'Subida de documentos',
    description: 'Permite subir y procesar documentos PDF para estudio',
    category: 'core',
    minimumPlans: ['free', 'usuario', 'pro'],
    requiresPayment: false,
    tokensRequired: 0,
    icon: 'FiUpload',
    route: '/app'
  },
  [FEATURE_IDS.TEST_GENERATION]: {
    id: FEATURE_IDS.TEST_GENERATION,
    name: 'test_generation',
    displayName: 'Generación de tests',
    description: 'Genera tests automáticos basados en el contenido de estudio',
    category: 'core',
    minimumPlans: ['free', 'usuario', 'pro'],
    requiresPayment: false,
    tokensRequired: 12500, // ACTUALIZADO: +25% (era 10,000) - Basado en consumo real Gemini 2.5 Pro: 12,211 tokens
    icon: 'FiFileText',
    route: '/app/tests'
  },
  [FEATURE_IDS.FLASHCARD_GENERATION]: {
    id: FEATURE_IDS.FLASHCARD_GENERATION,
    name: 'flashcard_generation',
    displayName: 'Generación de flashcards',
    description: 'Crea flashcards inteligentes para memorización efectiva',
    category: 'core',
    minimumPlans: ['free', 'usuario', 'pro'],
    requiresPayment: false,
    tokensRequired: 10000,
    icon: 'FiLayers',
    route: '/app/flashcards'
  },
  [FEATURE_IDS.MIND_MAP_GENERATION]: {
    id: FEATURE_IDS.MIND_MAP_GENERATION,
    name: 'mind_map_generation',
    displayName: 'Generación de mapas mentales',
    description: 'Genera mapas mentales visuales para mejor comprensión',
    category: 'core',
    minimumPlans: ['free', 'usuario', 'pro'],
    requiresPayment: false,
    tokensRequired: 16000, // ACTUALIZADO: +60% (era 10,000) - Basado en consumo real promedio: 14,673 tokens
    icon: 'FiGitBranch',
    route: '/app/mindmaps'
  },
  [FEATURE_IDS.AI_TUTOR_CHAT]: {
    id: FEATURE_IDS.AI_TUTOR_CHAT,
    name: 'ai_tutor_chat',
    displayName: 'Chat con preparador IA',
    description: 'Interactúa con un preparador de oposiciones inteligente',
    category: 'premium',
    minimumPlans: ['usuario', 'pro'],
    requiresPayment: true,
    tokensRequired: 20000,
    icon: 'FiMessageSquare',
    route: '/app/ai-tutor'
  },
  [FEATURE_IDS.STUDY_PLANNING]: {
    id: FEATURE_IDS.STUDY_PLANNING,
    name: 'study_planning',
    displayName: 'Planificación de estudios',
    description: 'Crea planes de estudio personalizados y estructurados',
    category: 'advanced',
    minimumPlans: ['pro'],
    requiresPayment: true,
    tokensRequired: 30000,
    icon: 'FiCalendar',
    route: '/plan-estudios'
  },
  [FEATURE_IDS.SUMMARY_A1_A2]: {
    id: FEATURE_IDS.SUMMARY_A1_A2,
    name: 'summary_a1_a2',
    displayName: 'Resúmenes A1 y A2',
    description: 'Genera resúmenes especializados para oposiciones A1 y A2',
    category: 'advanced',
    minimumPlans: ['pro'],
    requiresPayment: true,
    tokensRequired: 25000, // ACTUALIZADO: +67% (era 15,000) - Basado en consumo real fase final: 18,546 tokens + margen
    icon: 'FiBook',
    route: '/app/summaries'
  }
};

// ============================================================================
// MAPEOS Y UTILIDADES
// ============================================================================

/**
 * Mapeo de acciones a características
 */
export const ACTION_TO_FEATURE_MAP: Record<ActionType, FeatureId> = {
  [ACTION_TYPES.TEST_GENERATION]: FEATURE_IDS.TEST_GENERATION,
  [ACTION_TYPES.FLASHCARD_GENERATION]: FEATURE_IDS.FLASHCARD_GENERATION,
  [ACTION_TYPES.MIND_MAP_GENERATION]: FEATURE_IDS.MIND_MAP_GENERATION,
  [ACTION_TYPES.AI_CHAT]: FEATURE_IDS.AI_TUTOR_CHAT,
  [ACTION_TYPES.STUDY_PLANNING]: FEATURE_IDS.STUDY_PLANNING,
  [ACTION_TYPES.SUMMARY_GENERATION]: FEATURE_IDS.SUMMARY_A1_A2
};

/**
 * Mapeo de actividades de tokens a características
 */
export const ACTIVITY_TO_FEATURE_MAP: Record<string, FeatureId> = {
  'test_generation': FEATURE_IDS.TEST_GENERATION,
  'flashcard_generation': FEATURE_IDS.FLASHCARD_GENERATION,
  'mind_map_generation': FEATURE_IDS.MIND_MAP_GENERATION,
  'ai_tutor_chat': FEATURE_IDS.AI_TUTOR_CHAT,
  'study_planning': FEATURE_IDS.STUDY_PLANNING,
  'summary_a1_a2': FEATURE_IDS.SUMMARY_A1_A2,
  'document_analysis': FEATURE_IDS.DOCUMENT_UPLOAD
};

/**
 * Configuración de rutas restringidas por plan
 */
export const PLAN_RESTRICTED_ROUTES: Record<string, string[]> = {
  '/plan-estudios': ['pro'],
  '/app/ai-tutor': ['usuario', 'pro'],
  '/app/summaries': ['pro'],
  '/app/advanced-features': ['pro']
};

// ============================================================================
// FUNCIONES UTILITARIAS
// ============================================================================

/**
 * Obtiene la configuración de una característica
 */
export function getFeatureConfig(featureId: FeatureId): FeatureConfig | undefined {
  return FEATURES_CONFIG[featureId];
}

/**
 * Obtiene el nombre para mostrar de una característica
 */
export function getFeatureDisplayName(featureId: string): string {
  const config = FEATURES_CONFIG[featureId as FeatureId];
  return config?.displayName || featureId;
}

/**
 * Obtiene todas las características de una categoría
 */
export function getFeaturesByCategory(category: 'core' | 'premium' | 'advanced'): FeatureConfig[] {
  return Object.values(FEATURES_CONFIG).filter(feature => feature.category === category);
}

/**
 * Obtiene las características disponibles para un plan
 */
export function getFeaturesForPlan(planId: string): FeatureConfig[] {
  return Object.values(FEATURES_CONFIG).filter(feature => 
    feature.minimumPlans.includes(planId)
  );
}

/**
 * Verifica si una característica requiere pago
 */
export function featureRequiresPayment(featureId: FeatureId): boolean {
  const config = getFeatureConfig(featureId);
  return config?.requiresPayment || false;
}

/**
 * Obtiene los tokens requeridos para una característica
 */
export function getFeatureTokensRequired(featureId: FeatureId): number {
  const config = getFeatureConfig(featureId);
  return config?.tokensRequired || 0;
}

/**
 * Convierte una acción a su característica correspondiente
 */
export function actionToFeature(action: ActionType): FeatureId {
  return ACTION_TO_FEATURE_MAP[action];
}

/**
 * Convierte una actividad a su característica correspondiente
 */
export function activityToFeature(activity: string): FeatureId | undefined {
  return ACTIVITY_TO_FEATURE_MAP[activity];
}

/**
 * Obtiene todas las características como array
 */
export function getAllFeatures(): FeatureConfig[] {
  return Object.values(FEATURES_CONFIG);
}

/**
 * Obtiene los IDs de todas las características
 */
export function getAllFeatureIds(): FeatureId[] {
  return Object.keys(FEATURES_CONFIG) as FeatureId[];
}

/**
 * Verifica si un ID de característica es válido
 */
export function isValidFeatureId(featureId: string): featureId is FeatureId {
  return featureId in FEATURES_CONFIG;
}

// ============================================================================
// MONITOREO Y ANÁLISIS DE CONSUMO
// ============================================================================

/**
 * Límites de alerta para monitoreo de consumo basados en análisis real
 */
export const TOKEN_MONITORING = {
  // Porcentaje de uso que dispara alertas
  WARNING_THRESHOLD: 0.8,  // 80% del límite
  CRITICAL_THRESHOLD: 0.95, // 95% del límite

  // Consumo promedio observado por funcionalidad (tokens)
  OBSERVED_AVERAGES: {
    [FEATURE_IDS.DOCUMENT_UPLOAD]: 0,          // No consume tokens
    [FEATURE_IDS.TEST_GENERATION]: 12211,      // Gemini 2.5 Pro
    [FEATURE_IDS.FLASHCARD_GENERATION]: 8068,  // Promedio observado
    [FEATURE_IDS.MIND_MAP_GENERATION]: 14673,  // Promedio observado
    [FEATURE_IDS.AI_TUTOR_CHAT]: 14850,        // Gemini 2.5 Flash
    [FEATURE_IDS.STUDY_PLANNING]: 25000,       // Estimado
    [FEATURE_IDS.SUMMARY_A1_A2]: 18546,        // Solo fase final (Reduce)
  },

  // Rangos observados (min-max tokens)
  OBSERVED_RANGES: {
    [FEATURE_IDS.DOCUMENT_UPLOAD]: { min: 0, max: 0 },      // No consume tokens
    [FEATURE_IDS.TEST_GENERATION]: { min: 6824, max: 18059 },
    [FEATURE_IDS.FLASHCARD_GENERATION]: { min: 8068, max: 8068 },
    [FEATURE_IDS.MIND_MAP_GENERATION]: { min: 13973, max: 15372 },
    [FEATURE_IDS.AI_TUTOR_CHAT]: { min: 11523, max: 14850 },
    [FEATURE_IDS.STUDY_PLANNING]: { min: 25000, max: 35000 }, // Estimado
    [FEATURE_IDS.SUMMARY_A1_A2]: { min: 4200, max: 18546 }, // Rango Map-Reduce
  }
} as const;

/**
 * Configuración de modelos asignados por funcionalidad (Vertex AI)
 */
export const MODEL_ASSIGNMENTS = {
  [FEATURE_IDS.DOCUMENT_UPLOAD]: 'none',                   // No usa IA
  [FEATURE_IDS.TEST_GENERATION]: 'gemini-2.5-pro',        // Alta precisión
  [FEATURE_IDS.FLASHCARD_GENERATION]: 'gemini-2.5-flash', // Rapidez y economía
  [FEATURE_IDS.MIND_MAP_GENERATION]: 'gemini-2.5-pro',    // Creatividad y estructura
  [FEATURE_IDS.AI_TUTOR_CHAT]: 'gemini-2.5-flash',        // Conversaciones rápidas
  [FEATURE_IDS.STUDY_PLANNING]: 'gemini-2.5-pro',         // Planificación compleja
  [FEATURE_IDS.SUMMARY_A1_A2]: 'gemini-2.5-flash',        // Síntesis económica
} as const;

/**
 * Costos promedio por operación en USD (basado en análisis real)
 */
export const OPERATION_COSTS = {
  [FEATURE_IDS.DOCUMENT_UPLOAD]: 0.0000,      // Sin costo
  [FEATURE_IDS.TEST_GENERATION]: 0.0602,      // Gemini 2.5 Pro observado
  [FEATURE_IDS.FLASHCARD_GENERATION]: 0.0050, // Estimado con Flash
  [FEATURE_IDS.MIND_MAP_GENERATION]: 0.0800,  // Estimado con Pro
  [FEATURE_IDS.AI_TUTOR_CHAT]: 0.0086,        // Gemini 2.5 Flash observado
  [FEATURE_IDS.STUDY_PLANNING]: 0.1500,       // Estimado Pro
  [FEATURE_IDS.SUMMARY_A1_A2]: 0.0500,        // Proceso Map-Reduce con Flash (85% más barato)
} as const;

// ============================================================================
// FUNCIONES UTILITARIAS DE MONITOREO
// ============================================================================

/**
 * Verifica si el consumo está dentro del límite seguro (80%)
 */
export function isWithinSafeLimit(featureId: FeatureId, tokensUsed: number): boolean {
  const config = getFeatureConfig(featureId);
  if (!config) return false;

  const safeLimit = config.tokensRequired * TOKEN_MONITORING.WARNING_THRESHOLD;
  return tokensUsed <= safeLimit;
}

/**
 * Obtiene el nivel de alerta para el consumo de tokens
 */
export function getTokenUsageAlert(featureId: FeatureId, tokensUsed: number): 'safe' | 'warning' | 'critical' | 'exceeded' {
  const config = getFeatureConfig(featureId);
  if (!config) return 'safe';

  const limit = config.tokensRequired;
  const warningThreshold = limit * TOKEN_MONITORING.WARNING_THRESHOLD;
  const criticalThreshold = limit * TOKEN_MONITORING.CRITICAL_THRESHOLD;

  if (tokensUsed > limit) return 'exceeded';
  if (tokensUsed > criticalThreshold) return 'critical';
  if (tokensUsed > warningThreshold) return 'warning';
  return 'safe';
}

/**
 * Obtiene el costo estimado para una operación
 */
export function getEstimatedCost(featureId: FeatureId): number {
  return OPERATION_COSTS[featureId] || 0;
}

/**
 * Obtiene el modelo asignado para una funcionalidad
 */
export function getAssignedModel(featureId: FeatureId): string {
  return MODEL_ASSIGNMENTS[featureId] || 'gemini-2.5-flash';
}

/**
 * Obtiene el consumo promedio observado para una funcionalidad
 */
export function getObservedAverage(featureId: FeatureId): number {
  return TOKEN_MONITORING.OBSERVED_AVERAGES[featureId] || 0;
}

/**
 * Calcula el porcentaje de uso respecto al límite configurado
 */
export function getUsagePercentage(featureId: FeatureId, tokensUsed: number): number {
  const config = getFeatureConfig(featureId);
  if (!config || config.tokensRequired === 0) return 0;

  return Math.round((tokensUsed / config.tokensRequired) * 100);
}
