import { prepararDocumentos } from '../vertexai/vertexAiClient';
import { PROMPT_FLASHCARDS } from '../../config/prompts';
import { llamarGemini } from '../vertexai/vertexAiClient';
import { getVertexAIConfig } from '@/config/vertexai';
import { type Chunk } from '@/lib/utils/textProcessing';
import { ChunkingService } from '../services/chunkingService';
import * as Sentry from "@sentry/nextjs";

/**
 * Genera flashcards a partir de los documentos
 */
export async function generarFlashcards(
  documentos: { titulo: string; contenido: string; categoria?: string; numero_tema?: number }[],
  cantidad: number = 10,
  instrucciones?: string
): Promise<{ pregunta: string; respuesta: string }[]> {
  try {
    // Preparar el contenido de los documentos
    const resultadoDocumentos = prepararDocumentos(documentos);

    // Verificar si se usó chunking
    if (resultadoDocumentos.wasChunked && Array.isArray(resultadoDocumentos.content)) {
      console.log(`🃏 Procesando flashcards con chunking: ${resultadoDocumentos.content.length} chunks`);
      return await procesarFlashcardsConChunks(resultadoDocumentos.content, cantidad, instrucciones, documentos);
    } else {
      // Procesamiento tradicional para documentos sin chunking
      const contenidoDocumentos = Array.isArray(resultadoDocumentos.content)
        ? resultadoDocumentos.content.join('\n\n')
        : resultadoDocumentos.content;

      if (!contenidoDocumentos) {
        throw new Error("No se han proporcionado documentos para generar flashcards.");
      }

      console.log(`🃏 Procesando flashcards sin chunking`);
      return await procesarFlashcardsSinChunks(contenidoDocumentos, cantidad, instrucciones);
    }


  } catch (error) {
    console.error('Error al generar flashcards:', error);
    throw error;
  }
}

/**
 * Procesa flashcards con chunking - usa el sistema simple y robusto de ChunkingService
 */
async function procesarFlashcardsConChunks(
  chunks: Chunk[],
  cantidad: number,
  instrucciones?: string,
  documentos?: { titulo: string; contenido: string; categoria?: string; numero_tema?: number }[]
): Promise<{ pregunta: string; respuesta: string }[]> {
  // Usar el nuevo ChunkingService para procesar chunks
  const resultado = await ChunkingService.procesarConChunks(chunks, instrucciones || '', 'FLASHCARDS');

  console.log(`🃏 Chunks procesados: ${resultado.chunksUsados} de ${resultado.totalChunks} (estrategia: ${resultado.estrategiaUsada})`);

  // Usar el contenido procesado por ChunkingService
  const combinedContent = resultado.contenido;

  // Preparar el prompt para la generación
  let prompt = PROMPT_FLASHCARDS
    .replace('{documentos}', combinedContent)
    .replace('{cantidad}', cantidad.toString());

  if (instrucciones) {
    prompt = prompt.replace('{instrucciones}', `Instrucciones adicionales:\n- ${instrucciones}`);
  } else {
    prompt = prompt.replace('{instrucciones}', '');
  }

  // Generar las flashcards usando el contenido procesado
  const config = getVertexAIConfig('FLASHCARDS');
  const responseText = await llamarGemini(prompt, {
    ...config,
    activityName: `Generación de Flashcards - ${resultado.chunksUsados}/${resultado.totalChunks} chunks (${resultado.estrategiaUsada})`
  });

  // Parsear la respuesta
  const jsonMatch = responseText.match(/\[\s*\{[\s\S]*\}\s*\]/);
  if (!jsonMatch) {
    throw new Error("La IA no devolvió un formato JSON válido.");
  }

  const flashcardsJson = jsonMatch[0];
  const flashcards = JSON.parse(flashcardsJson);

  // Validar formato básico
  if (!Array.isArray(flashcards) || flashcards.length === 0) {
    throw new Error("La IA no generó flashcards válidas.");
  }

  // Validar estructura de cada flashcard
  const flashcardsValidas = flashcards.filter(flashcard =>
    flashcard.pregunta &&
    flashcard.respuesta
  );

  if (flashcardsValidas.length === 0) {
    throw new Error("Ninguna de las flashcards generadas tiene el formato correcto.");
  }

  console.log(`🃏 Flashcards generadas con chunking combinado: ${flashcardsValidas.length} tarjetas finales`);
  return flashcardsValidas;
}

/**
 * Procesa flashcards sin chunking - método tradicional
 */
async function procesarFlashcardsSinChunks(
  contenidoDocumentos: string,
  cantidad: number,
  instrucciones?: string
): Promise<{ pregunta: string; respuesta: string }[]> {
  // Construir el prompt para la IA usando el prompt personalizado
  let prompt = PROMPT_FLASHCARDS
    .replace('{documentos}', contenidoDocumentos)
    .replace('{cantidad}', cantidad.toString());

  // Añadir instrucciones adicionales si se proporcionan
  if (instrucciones) {
    prompt = prompt.replace('{instrucciones}', `Instrucciones adicionales:\n- ${instrucciones}`);
  } else {
    prompt = prompt.replace('{instrucciones}', '');
  }

  // Obtener configuración específica para flashcards
  const config = getVertexAIConfig('FLASHCARDS');

  console.log(`🃏 Generando flashcards con modelo: ${config.model} (maxOutputTokens: ${config.maxOutputTokens})`);

  // Generar las flashcards usando Vertex AI con la configuración correcta
  const responseText = await llamarGemini(prompt, {
    ...config,
    activityName: `Generación de Flashcards (${cantidad || 'N/A'} tarjetas)`
  });

  // Extraer el JSON de la respuesta
  const jsonMatch = responseText.match(/\[\s*\{[\s\S]*\}\s*\]/);

  if (!jsonMatch) {
    throw new Error("No se pudo extraer el formato JSON de la respuesta.");
  }

  const flashcardsJson = jsonMatch[0];
  const flashcards = JSON.parse(flashcardsJson);

  // Validar el formato
  if (!Array.isArray(flashcards) || flashcards.length === 0) {
    throw new Error("El formato de las flashcards generadas no es válido.");
  }

  return flashcards;
}
